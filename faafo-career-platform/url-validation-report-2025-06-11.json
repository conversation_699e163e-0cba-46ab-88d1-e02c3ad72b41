{"timestamp": "2025-06-11T21:54:08.949Z", "totalResources": 73, "successRate": 88, "results": {"working": [{"id": "c03cfed9-1cd7-4ae7-ad59-e4f0c1a06f42", "title": "AI For Everyone", "url": "https://www.coursera.org/learn/ai-for-everyone", "author": "DeepLearning.AI", "category": "ARTIFICIAL_INTELLIGENCE", "status": 200, "redirectUrl": null}, {"id": "e37d657c-9e78-405b-b020-239560fe3f53", "title": "AWS Cloud Practitioner Essentials", "url": "https://aws.amazon.com/training/learn-about/cloud-practitioner/", "author": "Amazon Web Services", "category": "DEVOPS", "status": 200, "redirectUrl": null}, {"id": "94bdc831-486d-4b14-ac18-c62c5170ab86", "title": "AWS DevOps Engineer Professional", "url": "https://aws.amazon.com/certification/certified-devops-engineer-professional/", "author": "Amazon Web Services", "category": "DEVOPS", "status": 200, "redirectUrl": null}, {"id": "19605904-a81e-42cb-ab6c-2f079f1a88a1", "title": "AWS Solutions Architect Associate", "url": "https://aws.amazon.com/training/classroom/architecting-on-aws/", "author": "Amazon Web Services", "category": "DEVOPS", "status": 200, "redirectUrl": null}, {"id": "2a8d3059-1721-4c7e-9e8e-eec28dc219a8", "title": "AWS Well-Architected Framework", "url": "https://aws.amazon.com/architecture/well-architected/", "author": "Amazon Web Services", "category": "DEVOPS", "status": 200, "redirectUrl": null}, {"id": "d0cd695f-8681-4eb4-83f4-39c3211bc23b", "title": "Advanced Financial Modeling", "url": "https://www.wallstreetmojo.com/financial-modeling/", "author": "WallStreetMojo", "category": "FINANCIAL_LITERACY", "status": 200, "redirectUrl": null}, {"id": "37e78370-f958-485a-bd0a-6a2ffb70fcfd", "title": "Advanced Investment Strategies", "url": "https://www.edx.org/learn/investing/indian-institute-of-management-bangalore-introduction-to-investments", "author": "Indian Institute of Management", "category": "FINANCIAL_LITERACY", "status": 200, "redirectUrl": null}, {"id": "871ab705-f05c-4cec-a049-cbfb04af2268", "title": "Advanced React Patterns", "url": "https://kentcdodds.com/blog/advanced-react-component-patterns", "author": "Kent <PERSON>", "category": "WEB_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "7549cc9e-40dc-47cd-b642-d4685c997c68", "title": "Advanced iOS Development", "url": "https://developer.apple.com/documentation/technologies", "author": "Apple Developer", "category": "MOBILE_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "3b3414f8-43e9-4ed6-a2ad-a67455102849", "title": "Agile Development Specialization", "url": "https://www.coursera.org/specializations/agile-development", "author": "University of Virginia", "category": "PRODUCT_MANAGEMENT", "status": 200, "redirectUrl": null}, {"id": "b5b217df-8832-4e91-b0b2-0a7961115699", "title": "Android Architecture Components", "url": "https://developer.android.com/topic/architecture", "author": "Google Android", "category": "MOBILE_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "e83bb12d-ea31-460d-a9fc-326974c8f725", "title": "Android Development Fundamentals", "url": "https://developer.android.com/courses/android-basics-compose/course", "author": "Google Android", "category": "MOBILE_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "b163b8f9-e083-40d1-b4c0-3415fd82abea", "title": "App Store Optimization (ASO)", "url": "https://developer.apple.com/app-store/product-page/", "author": "Apple Developer", "category": "MOBILE_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "f4a11319-f27d-4e3b-be7a-87437170ee7d", "title": "Blockchain Basics", "url": "https://www.coursera.org/learn/blockchain-basics", "author": "University at Buffalo", "category": "BLOCKCHAIN", "status": 200, "redirectUrl": null}, {"id": "f4f685a0-5c6c-4520-98b5-8566ae92dcbf", "title": "Blockchain Fundamentals", "url": "https://www.edx.org/certificates/professional-certificate/uc-berkeleyx-blockchain-fundamentals", "author": "UC Berkeley", "category": "BLOCKCHAIN", "status": 200, "redirectUrl": null}, {"id": "7070dfb6-4cf4-4db5-a669-2d2b5ad13205", "title": "CISA Learning", "url": "https://www.cisa.gov/cybersecurity-training-exercises", "author": "CISA", "category": "CYBERSECURITY", "status": 200, "redirectUrl": null}, {"id": "f5ca2bc2-4fd2-494c-969a-61a25792cc93", "title": "Certified in Cybersecurity (CC)", "url": "https://www.isc2.org/landing/1mcc", "author": "ISC2", "category": "CYBERSECURITY", "status": 200, "redirectUrl": null}, {"id": "6dc834ba-606b-4463-9038-c59efad1808e", "title": "Cloud Security Essentials (C|SE)", "url": "https://www.eccouncil.org/train-certify/cloud-security-essentials-cse/", "author": "EC-Council", "category": "CYBERSECURITY", "status": 200, "redirectUrl": null}, {"id": "4351ed1c-9e25-4680-8146-d8f5c984744c", "title": "Coursera Product Management Course", "url": "https://www.coursera.org/learn/product-management", "author": "University of Virginia", "category": "PRODUCT_MANAGEMENT", "status": 200, "redirectUrl": null}, {"id": "3ed4c1d4-d85e-4825-ba95-262eb10aa59b", "title": "Customer Development and Validation", "url": "https://steveblank.com/category/customer-development/", "author": "<PERSON>", "category": "ENTREPRENEURSHIP", "status": 200, "redirectUrl": null}, {"id": "9573dc19-224e-4e0d-99d1-4b67a3ef5d06", "title": "Cybersecurity Fundamentals", "url": "https://skillsbuild.org/students/course-catalog/cybersecurity", "author": "IBM SkillsBuild", "category": "CYBERSECURITY", "status": 200, "redirectUrl": null}, {"id": "b3ac729c-e6e3-4cbf-8485-ad7eff254953", "title": "Data Science: Machine Learning", "url": "https://pll.harvard.edu/course/data-science-machine-learning", "author": "Harvard University", "category": "DATA_SCIENCE", "status": 200, "redirectUrl": null}, {"id": "9eb865c0-69c1-4309-970e-7f52926c90a6", "title": "Deep Learning Specialization", "url": "https://www.coursera.org/specializations/deep-learning", "author": "DeepLearning.AI", "category": "ARTIFICIAL_INTELLIGENCE", "status": 200, "redirectUrl": null}, {"id": "558e0f5a-58f9-47e2-86e0-0181b3812baf", "title": "Design Systems with Figma", "url": "https://www.figma.com/resource-library/design-basics/", "author": "Figma", "category": "UX_UI_DESIGN", "status": 200, "redirectUrl": null}, {"id": "077cd1ca-6bc2-4fd1-a1f1-b78b0867517d", "title": "Digital Forensics Essentials (D|FE)", "url": "https://www.eccouncil.org/train-certify/digital-forensics-essentials-dfe/", "author": "EC-Council", "category": "CYBERSECURITY", "status": 200, "redirectUrl": null}, {"id": "630167c1-1e0e-4b53-b2f8-60670f5394e5", "title": "Docker Getting Started", "url": "https://docs.docker.com/get-started/", "author": "<PERSON>er", "category": "DEVOPS", "status": 200, "redirectUrl": null}, {"id": "c8675a4f-5cd7-4a82-9a02-49ccd68f1cd0", "title": "Elements of AI", "url": "https://www.elementsofai.com/", "author": "University of Helsinki", "category": "ARTIFICIAL_INTELLIGENCE", "status": 200, "redirectUrl": null}, {"id": "76c22147-041f-4513-93a3-70d72387bd14", "title": "Emergency Fund Building Guide", "url": "https://www.khanacademy.org/economics-finance-domain/core-finance/investment-vehicles-tutorial/ira-401ks/v/emergency-fund", "author": "Khan Academy", "category": "FINANCIAL_LITERACY", "status": 200, "redirectUrl": null}, {"id": "caffdbd6-f2f2-4fbb-bb0b-4fa646dcc32b", "title": "Ethical Hacking Essentials (E|HE)", "url": "https://www.eccouncil.org/train-certify/ethical-hacking-essentials-ehe/", "author": "EC-Council", "category": "CYBERSECURITY", "status": 200, "redirectUrl": null}, {"id": "4d79d7f9-05a8-47c8-a4e9-1ced694176d5", "title": "Financial Planning for Entrepreneurs", "url": "https://www.sba.gov/business-guide/plan-your-business/calculate-your-startup-costs", "author": "Small Business Administration", "category": "FINANCIAL_LITERACY", "status": 200, "redirectUrl": null}, {"id": "acfa659e-415a-4f25-8dc7-741456616a09", "title": "Flutter Development Bootcamp", "url": "https://docs.flutter.dev/get-started/codelab", "author": "Google Flutter", "category": "MOBILE_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "e13e057c-d7ee-4da9-a837-95f6080eeec9", "title": "Google Project Management Certificate", "url": "https://www.coursera.org/professional-certificates/google-project-management", "author": "Google", "category": "PROJECT_MANAGEMENT", "status": 200, "redirectUrl": null}, {"id": "53bcee8b-ce3a-4686-8a50-0e06ac6105c1", "title": "How to Budget as a Freelancer - Complete Guide", "url": "https://found.com/resources/how-to-budget-as-a-freelancer", "author": "Found", "category": "FINANCIAL_LITERACY", "status": 200, "redirectUrl": null}, {"id": "9fddd4d3-0097-45e9-b83d-52ba93bafb75", "title": "Interaction Design Foundation", "url": "https://www.interaction-design.org/", "author": "IxDF", "category": "UX_UI_DESIGN", "status": 200, "redirectUrl": null}, {"id": "d3afd227-1b9e-42fe-935a-7e2f7108359c", "title": "Interaction Design Foundation UX Course", "url": "https://www.interaction-design.org/courses/user-experience-the-beginner-s-guide", "author": "Interaction Design Foundation", "category": "UX_UI_DESIGN", "status": 200, "redirectUrl": null}, {"id": "f6af7408-dfd2-41db-883c-2a3fd67438e2", "title": "Introduction to Data Science", "url": "https://skillsbuild.org/students/course-catalog/data-science", "author": "IBM SkillsBuild", "category": "DATA_SCIENCE", "status": 200, "redirectUrl": null}, {"id": "bd97b3d6-8767-4d26-983e-57da3bd90083", "title": "JavaScript.info - Modern JavaScript Tutorial", "url": "https://javascript.info/", "author": "<PERSON><PERSON>", "category": "WEB_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "c1e0e164-6390-43bd-84bd-3200009414d3", "title": "Khan Academy Personal Finance", "url": "https://www.khanacademy.org/college-careers-more/personal-finance", "author": "Khan Academy", "category": "FINANCIAL_LITERACY", "status": 200, "redirectUrl": null}, {"id": "e03ccf2a-82c0-4131-b295-d659a28cec2b", "title": "Kubernetes Basics", "url": "https://kubernetes.io/docs/tutorials/kubernetes-basics/", "author": "Kubernetes", "category": "DEVOPS", "status": 200, "redirectUrl": null}, {"id": "4c091ece-e0f7-4e71-b8eb-4dbfc1445c45", "title": "MDN Web Docs", "url": "https://developer.mozilla.org/en-US/docs/Learn_web_development", "author": "Mozilla", "category": "WEB_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "624981d6-c7ef-450f-b402-12c6b1d575f0", "title": "MLOps Specialization", "url": "https://www.coursera.org/learn/introduction-to-machine-learning-in-production", "author": "DeepLearning.AI", "category": "ARTIFICIAL_INTELLIGENCE", "status": 200, "redirectUrl": null}, {"id": "e8d4b1b4-6317-4a75-8535-b9ae71dd5a94", "title": "Machine Learning Crash Course", "url": "https://developers.google.com/machine-learning/crash-course", "author": "Google", "category": "ARTIFICIAL_INTELLIGENCE", "status": 200, "redirectUrl": null}, {"id": "93f8c434-e451-4958-98bb-3d1d16467647", "title": "Material Design Guidelines", "url": "https://m3.material.io/", "author": "Google", "category": "UX_UI_DESIGN", "status": 200, "redirectUrl": null}, {"id": "f99bd221-a7d1-4f44-a262-fba66b5be876", "title": "Microsoft Azure Fundamentals", "url": "https://learn.microsoft.com/en-us/learn/paths/azure-fundamentals/", "author": "Microsoft", "category": "DEVOPS", "status": 200, "redirectUrl": null}, {"id": "e17988f8-ffcc-46d7-8cf3-bedc0a93b9bf", "title": "Mobile App Testing and Deployment", "url": "https://firebase.google.com/docs/app-distribution", "author": "Google Firebase", "category": "MOBILE_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "1b90c410-29e9-4e9d-b9d0-f01b36cdfaff", "title": "Mobile UI/UX Design Principles", "url": "https://m2.material.io/design/introduction/", "author": "Google Material Design", "category": "MOBILE_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "881a56ef-2208-4614-8c74-bb7bf76c0487", "title": "NIST Cybersecurity Framework Guide", "url": "https://www.nist.gov/cyberframework", "author": "NIST", "category": "CYBERSECURITY", "status": 200, "redirectUrl": null}, {"id": "d904fbcb-9e45-4c14-885b-3217af375a65", "title": "Network Defense Essentials (N|DE)", "url": "https://www.eccouncil.org/train-certify/network-defense-essentials-nde/", "author": "EC-Council", "category": "CYBERSECURITY", "status": 200, "redirectUrl": null}, {"id": "ca24b7b2-33d1-44cf-992a-49e37ed9c6fd", "title": "Node.js Developer Roadmap", "url": "https://nodejs.org/en/learn/getting-started/introduction-to-nodejs", "author": "Node.js Foundation", "category": "WEB_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "b326b399-6f7a-4b6b-b7df-d3d5a4932ced", "title": "OWASP Top 10 Security Risks", "url": "https://owasp.org/www-project-top-ten/", "author": "OWASP", "category": "CYBERSECURITY", "status": 200, "redirectUrl": null}, {"id": "4eec8674-af9e-4f6d-bbc2-2b4f90bfef01", "title": "Personal Financial Planning Fundamentals", "url": "https://www.investopedia.com/articles/personal-finance/100516/setting-financial-goals/", "author": "Investopedia", "category": "FINANCIAL_LITERACY", "status": 200, "redirectUrl": null}, {"id": "1906a5bb-a5d4-4d55-adb5-9d6ddfb47743", "title": "Product Management Fundamentals", "url": "https://www.coursera.org/learn/uva-darden-digital-product-management", "author": "University of Virginia", "category": "PRODUCT_MANAGEMENT", "status": 200, "redirectUrl": null}, {"id": "d9be23f8-b04a-460b-9fe6-a145ea94d67f", "title": "Project Management Foundations", "url": "https://www.linkedin.com/learning/project-management-foundations-15528659", "author": "LinkedIn Learning", "category": "PROJECT_MANAGEMENT", "status": 200, "redirectUrl": null}, {"id": "3e6cdfd4-79ea-41b5-9902-95ce34708037", "title": "Python for Data Science and AI", "url": "https://www.coursera.org/learn/python-for-applied-data-science-ai", "author": "IBM", "category": "ARTIFICIAL_INTELLIGENCE", "status": 200, "redirectUrl": null}, {"id": "f2f90e6e-8c12-4f63-afa6-22cfee4a6a92", "title": "React Native Complete Guide", "url": "https://reactnative.dev/docs/tutorial", "author": "Meta", "category": "MOBILE_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "8d38f130-da85-44f4-a005-274b10b272fb", "title": "React Official Tutorial", "url": "https://react.dev/learn", "author": "Meta", "category": "WEB_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "f3b7b699-29a9-406a-bd6c-b6e3bfa9b743", "title": "Sales and Customer Acquisition", "url": "https://www.hubspot.com/sales/sales-training", "author": "HubSpot", "category": "ENTREPRENEURSHIP", "status": 200, "redirectUrl": null}, {"id": "5677049e-0c0f-4c91-8ef7-9c25659bdf8d", "title": "Social Media Marketing", "url": "https://academy.hubspot.com/courses/social-media", "author": "HubSpot Academy", "category": "DIGITAL_MARKETING", "status": 200, "redirectUrl": null}, {"id": "99a73589-e390-4d5a-88e3-c117f4d46bd6", "title": "Tax Planning for Career Transitions", "url": "https://www.irs.gov/businesses/small-businesses-self-employed/self-employed-individuals-tax-center", "author": "IRS", "category": "FINANCIAL_LITERACY", "status": 200, "redirectUrl": null}, {"id": "7d5b04ab-03a6-464a-8e9c-dd9b7a08be40", "title": "Terraform Getting Started", "url": "https://developer.hashicorp.com/terraform/tutorials", "author": "HashiCorp", "category": "DEVOPS", "status": 200, "redirectUrl": null}, {"id": "55322feb-8346-4e5c-b6fd-77011c186685", "title": "Toastmasters Communication Skills", "url": "https://www.toastmasters.org/education", "author": "Toastmasters International", "category": "LANGUAGE_LEARNING", "status": 200, "redirectUrl": null}, {"id": "a5ec2e44-7468-43ee-a936-a1989e6ce518", "title": "Y Combinator Startup School", "url": "https://www.startupschool.org/", "author": "Y Combinator", "category": "ENTREPRENEURSHIP", "status": 200, "redirectUrl": null}, {"id": "dd2b17cc-edcc-432d-baa7-9623bcd08632", "title": "freeCodeCamp Full Stack Development", "url": "https://www.freecodecamp.org/", "author": "freeCodeCamp", "category": "WEB_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "0b2334ff-d13a-4aff-bb31-54f4070f7d41", "title": "iOS App Development for Beginners", "url": "https://developer.apple.com/tutorials/swiftui", "author": "Apple Developer", "category": "MOBILE_DEVELOPMENT", "status": 200, "redirectUrl": null}], "broken": [{"id": "95d2b33c-e5f0-454d-ba03-016e1be52175", "title": "Bogleheads Investment Philosophy", "url": "https://www.bogleheads.org/wiki/Bogleheads%C2%AE_investment_philosophy", "author": "Bogleheads Community", "category": "FINANCIAL_LITERACY", "status": 403, "redirectUrl": null}, {"id": "02eb5481-4951-4c5e-bdd4-af9197b4d2eb", "title": "Figma Design Basics", "url": "https://help.figma.com/hc/en-us/sections/4405269443991-Figma-design", "author": "Figma", "category": "UX_UI_DESIGN", "status": 404, "redirectUrl": null}, {"id": "e16cf00e-f631-4b90-89cc-148922539b69", "title": "Investment Basics for Beginners", "url": "https://www.investopedia.com/university/beginner", "author": "Investopedia", "category": "FINANCIAL_LITERACY", "status": 404, "redirectUrl": null}], "redirected": [{"id": "68ed50c0-793d-4ff1-b9d3-bef12a533dbd", "title": "Agile Alliance Resources", "url": "https://www.agilealliance.org/agile101/", "author": "Agile Alliance", "category": "PROJECT_MANAGEMENT", "status": 301, "redirectUrl": "https://agilealliance.org/agile101/"}, {"id": "8a701bb2-42c0-472a-b174-b1903c89679b", "title": "Business Model Canvas", "url": "https://www.strategyzer.com/library/business-model-canvas", "author": "Strategyzer", "category": "ENTREPRENEURSHIP", "status": 301, "redirectUrl": "/library/the-business-model-canvas"}, {"id": "382b42e5-79ce-4a81-ad13-a8f744d7b9a5", "title": "Fundamentals of Digital Marketing", "url": "https://grow.google", "author": "Google Digital Garage", "category": "DIGITAL_MARKETING", "status": 302, "redirectUrl": "/intl/nl/"}, {"id": "c01f7d67-16bc-4235-8b7a-81ac4b5f4eea", "title": "Machine Learning Engineering for Production", "url": "https://www.deeplearning.ai/courses/machine-learning-engineering-for-production-mlops/", "author": "DeepLearning.AI", "category": "ARTIFICIAL_INTELLIGENCE", "status": 308, "redirectUrl": "/courses/machine-learning-in-production"}, {"id": "4eb5e015-a78d-403e-b8ee-03a9312b4c65", "title": "SANS Cyber Aces Tutorials", "url": "https://cyberaces.org/courses.html", "author": "SANS Institute", "category": "CYBERSECURITY", "status": 301, "redirectUrl": "https://www.cyberaces.org/courses.html"}], "timeout": [{"id": "600486df-82b8-439b-b0e0-8952faf5fc9e", "title": "Adobe XD Tutorials", "url": "https://helpx.adobe.com/xd/tutorials.html", "author": "Adobe", "category": "UX_UI_DESIGN", "status": "TIMEOUT", "redirectUrl": null}], "errors": []}}