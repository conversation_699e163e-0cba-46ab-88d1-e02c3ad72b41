{"timestamp": "2025-06-11T21:34:32.149Z", "totalResources": 76, "successRate": 47, "results": {"working": [{"id": "c03cfed9-1cd7-4ae7-ad59-e4f0c1a06f42", "title": "AI For Everyone", "url": "https://www.coursera.org/learn/ai-for-everyone", "author": "DeepLearning.AI", "category": "ARTIFICIAL_INTELLIGENCE", "status": 200, "redirectUrl": null}, {"id": "94bdc831-486d-4b14-ac18-c62c5170ab86", "title": "AWS DevOps Engineer Professional", "url": "https://aws.amazon.com/certification/certified-devops-engineer-professional/", "author": "Amazon Web Services", "category": "DEVOPS", "status": 200, "redirectUrl": null}, {"id": "19605904-a81e-42cb-ab6c-2f079f1a88a1", "title": "AWS Solutions Architect Associate", "url": "https://aws.amazon.com/training/classroom/architecting-on-aws/", "author": "Amazon Web Services", "category": "DEVOPS", "status": 200, "redirectUrl": null}, {"id": "7549cc9e-40dc-47cd-b642-d4685c997c68", "title": "Advanced iOS Development", "url": "https://developer.apple.com/documentation/technologies", "author": "Apple Developer", "category": "MOBILE_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "3b3414f8-43e9-4ed6-a2ad-a67455102849", "title": "Agile Development Specialization", "url": "https://www.coursera.org/specializations/agile-development", "author": "University of Virginia", "category": "PRODUCT_MANAGEMENT", "status": 200, "redirectUrl": null}, {"id": "b5b217df-8832-4e91-b0b2-0a7961115699", "title": "Android Architecture Components", "url": "https://developer.android.com/topic/architecture", "author": "Google Android", "category": "MOBILE_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "b163b8f9-e083-40d1-b4c0-3415fd82abea", "title": "App Store Optimization (ASO)", "url": "https://developer.apple.com/app-store/product-page/", "author": "Apple Developer", "category": "MOBILE_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "f4a11319-f27d-4e3b-be7a-87437170ee7d", "title": "Blockchain Basics", "url": "https://www.coursera.org/learn/blockchain-basics", "author": "University at Buffalo", "category": "BLOCKCHAIN", "status": 200, "redirectUrl": null}, {"id": "7070dfb6-4cf4-4db5-a669-2d2b5ad13205", "title": "CISA Learning", "url": "https://www.cisa.gov/cybersecurity-training-exercises", "author": "CISA", "category": "CYBERSECURITY", "status": 200, "redirectUrl": null}, {"id": "f5ca2bc2-4fd2-494c-969a-61a25792cc93", "title": "Certified in Cybersecurity (CC)", "url": "https://www.isc2.org/landing/1mcc", "author": "ISC2", "category": "CYBERSECURITY", "status": 200, "redirectUrl": null}, {"id": "6dc834ba-606b-4463-9038-c59efad1808e", "title": "Cloud Security Essentials (C|SE)", "url": "https://www.eccouncil.org/train-certify/cloud-security-essentials-cse/", "author": "EC-Council", "category": "CYBERSECURITY", "status": 200, "redirectUrl": null}, {"id": "3ed4c1d4-d85e-4825-ba95-262eb10aa59b", "title": "Customer Development and Validation", "url": "https://steveblank.com/category/customer-development/", "author": "<PERSON>", "category": "ENTREPRENEURSHIP", "status": 200, "redirectUrl": null}, {"id": "9573dc19-224e-4e0d-99d1-4b67a3ef5d06", "title": "Cybersecurity Fundamentals", "url": "https://skillsbuild.org/students/course-catalog/cybersecurity", "author": "IBM SkillsBuild", "category": "CYBERSECURITY", "status": 200, "redirectUrl": null}, {"id": "b3ac729c-e6e3-4cbf-8485-ad7eff254953", "title": "Data Science: Machine Learning", "url": "https://pll.harvard.edu/course/data-science-machine-learning", "author": "Harvard University", "category": "DATA_SCIENCE", "status": 200, "redirectUrl": null}, {"id": "9eb865c0-69c1-4309-970e-7f52926c90a6", "title": "Deep Learning Specialization", "url": "https://www.coursera.org/specializations/deep-learning", "author": "DeepLearning.AI", "category": "ARTIFICIAL_INTELLIGENCE", "status": 200, "redirectUrl": null}, {"id": "077cd1ca-6bc2-4fd1-a1f1-b78b0867517d", "title": "Digital Forensics Essentials (D|FE)", "url": "https://www.eccouncil.org/train-certify/digital-forensics-essentials-dfe/", "author": "EC-Council", "category": "CYBERSECURITY", "status": 200, "redirectUrl": null}, {"id": "630167c1-1e0e-4b53-b2f8-60670f5394e5", "title": "Docker Getting Started", "url": "https://docs.docker.com/get-started/", "author": "<PERSON>er", "category": "DEVOPS", "status": 200, "redirectUrl": null}, {"id": "c8675a4f-5cd7-4a82-9a02-49ccd68f1cd0", "title": "Elements of AI", "url": "https://www.elementsofai.com/", "author": "University of Helsinki", "category": "ARTIFICIAL_INTELLIGENCE", "status": 200, "redirectUrl": null}, {"id": "76c22147-041f-4513-93a3-70d72387bd14", "title": "Emergency Fund Building Guide", "url": "https://www.khanacademy.org/economics-finance-domain/core-finance/investment-vehicles-tutorial/ira-401ks/v/emergency-fund", "author": "Khan Academy", "category": "FINANCIAL_LITERACY", "status": 200, "redirectUrl": null}, {"id": "caffdbd6-f2f2-4fbb-bb0b-4fa646dcc32b", "title": "Ethical Hacking Essentials (E|HE)", "url": "https://www.eccouncil.org/train-certify/ethical-hacking-essentials-ehe/", "author": "EC-Council", "category": "CYBERSECURITY", "status": 200, "redirectUrl": null}, {"id": "4d79d7f9-05a8-47c8-a4e9-1ced694176d5", "title": "Financial Planning for Entrepreneurs", "url": "https://www.sba.gov/business-guide/plan-your-business/calculate-your-startup-costs", "author": "Small Business Administration", "category": "FINANCIAL_LITERACY", "status": 200, "redirectUrl": null}, {"id": "53bcee8b-ce3a-4686-8a50-0e06ac6105c1", "title": "How to Budget as a Freelancer - Complete Guide", "url": "https://found.com/resources/how-to-budget-as-a-freelancer", "author": "Found", "category": "FINANCIAL_LITERACY", "status": 200, "redirectUrl": null}, {"id": "9fddd4d3-0097-45e9-b83d-52ba93bafb75", "title": "Interaction Design Foundation", "url": "https://www.interaction-design.org/", "author": "IxDF", "category": "UX_UI_DESIGN", "status": 200, "redirectUrl": null}, {"id": "f6af7408-dfd2-41db-883c-2a3fd67438e2", "title": "Introduction to Data Science", "url": "https://skillsbuild.org/students/course-catalog/data-science", "author": "IBM SkillsBuild", "category": "DATA_SCIENCE", "status": 200, "redirectUrl": null}, {"id": "e03ccf2a-82c0-4131-b295-d659a28cec2b", "title": "Kubernetes Basics", "url": "https://kubernetes.io/docs/tutorials/kubernetes-basics/", "author": "Kubernetes", "category": "DEVOPS", "status": 200, "redirectUrl": null}, {"id": "e8d4b1b4-6317-4a75-8535-b9ae71dd5a94", "title": "Machine Learning Crash Course", "url": "https://developers.google.com/machine-learning/crash-course", "author": "Google", "category": "ARTIFICIAL_INTELLIGENCE", "status": 200, "redirectUrl": null}, {"id": "e17988f8-ffcc-46d7-8cf3-bedc0a93b9bf", "title": "Mobile App Testing and Deployment", "url": "https://firebase.google.com/docs/app-distribution", "author": "Google Firebase", "category": "MOBILE_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "d904fbcb-9e45-4c14-885b-3217af375a65", "title": "Network Defense Essentials (N|DE)", "url": "https://www.eccouncil.org/train-certify/network-defense-essentials-nde/", "author": "EC-Council", "category": "CYBERSECURITY", "status": 200, "redirectUrl": null}, {"id": "3e6cdfd4-79ea-41b5-9902-95ce34708037", "title": "Python for Data Science and AI", "url": "https://www.coursera.org/learn/python-for-applied-data-science-ai", "author": "IBM", "category": "ARTIFICIAL_INTELLIGENCE", "status": 200, "redirectUrl": null}, {"id": "f2f90e6e-8c12-4f63-afa6-22cfee4a6a92", "title": "React Native Complete Guide", "url": "https://reactnative.dev/docs/tutorial", "author": "Meta", "category": "MOBILE_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "8d38f130-da85-44f4-a005-274b10b272fb", "title": "React Official Tutorial", "url": "https://react.dev/learn", "author": "Meta", "category": "WEB_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "5677049e-0c0f-4c91-8ef7-9c25659bdf8d", "title": "Social Media Marketing", "url": "https://academy.hubspot.com/courses/social-media", "author": "HubSpot Academy", "category": "DIGITAL_MARKETING", "status": 200, "redirectUrl": null}, {"id": "99a73589-e390-4d5a-88e3-c117f4d46bd6", "title": "Tax Planning for Career Transitions", "url": "https://www.irs.gov/businesses/small-businesses-self-employed/self-employed-individuals-tax-center", "author": "IRS", "category": "FINANCIAL_LITERACY", "status": 200, "redirectUrl": null}, {"id": "a5ec2e44-7468-43ee-a936-a1989e6ce518", "title": "Y Combinator Startup School", "url": "https://www.startupschool.org/", "author": "Y Combinator", "category": "ENTREPRENEURSHIP", "status": 200, "redirectUrl": null}, {"id": "dd2b17cc-edcc-432d-baa7-9623bcd08632", "title": "freeCodeCamp Full Stack Development", "url": "https://www.freecodecamp.org/", "author": "freeCodeCamp", "category": "WEB_DEVELOPMENT", "status": 200, "redirectUrl": null}, {"id": "0b2334ff-d13a-4aff-bb31-54f4070f7d41", "title": "iOS App Development for Beginners", "url": "https://developer.apple.com/tutorials/swiftui", "author": "Apple Developer", "category": "MOBILE_DEVELOPMENT", "status": 200, "redirectUrl": null}], "broken": [{"id": "e805d8be-1969-423d-a6a9-636c701d4e79", "title": "Building and Leading Teams", "url": "https://www.linkedin.com/learning/building-and-leading-teams", "author": "LinkedIn Learning", "category": "ENTREPRENEURSHIP", "status": 404, "redirectUrl": null}, {"id": "178b03f7-d58f-4596-b5a8-95874b1100e5", "title": "Business English for International Careers", "url": "https://www.futurelearn.com/courses/business-english-for-cross-cultural-communication", "author": "University of London", "category": "LANGUAGE_LEARNING", "status": 403, "redirectUrl": null}, {"id": "b40f50da-7460-4efe-9dae-414b47d64016", "title": "Cross-Cultural Communication", "url": "https://www.edx.org/course/intercultural-communication", "author": "University of California San Diego", "category": "LANGUAGE_LEARNING", "status": 404, "redirectUrl": null}, {"id": "1fb82e3a-a618-42b5-8748-b077aba35f65", "title": "Figma Academy", "url": "https://www.figma.com/academy/", "author": "Figma", "category": "UX_UI_DESIGN", "status": 404, "redirectUrl": null}, {"id": "71c66aaf-f09b-4f89-b164-6a7650d7cb3d", "title": "Google Cloud Fundamentals", "url": "https://cloud.google.com/training/courses/gcp-fundamentals", "author": "Google Cloud", "category": "DEVOPS", "status": 404, "redirectUrl": null}, {"id": "95a3a244-df6f-45a1-bf2e-8c399383164e", "title": "Introduction to Project Management - PM<PERSON>", "url": "https://www.pmi.org/learning/free-online-courses", "author": "Project Management Institute", "category": "PROJECT_MANAGEMENT", "status": 403, "redirectUrl": null}, {"id": "c0188540-f5d6-409d-81e1-2a0e96f89083", "title": "<PERSON><PERSON>", "url": "https://www.kaggle.com/learn", "author": "<PERSON><PERSON>", "category": "ARTIFICIAL_INTELLIGENCE", "status": 404, "redirectUrl": null}, {"id": "eb10b51c-ef67-455f-af34-e993e98b964c", "title": "Lean Startup Methodology", "url": "https://www.edx.org/course/entrepreneurship-micromaster", "author": "Babson College", "category": "ENTREPRENEURSHIP", "status": 404, "redirectUrl": null}, {"id": "f670c530-2e22-4026-ae1f-d8c02e2141c4", "title": "Legal Basics for Startups", "url": "https://www.nolo.com/legal-encyclopedia/small-business-startup", "author": "<PERSON><PERSON>", "category": "ENTREPRENEURSHIP", "status": 404, "redirectUrl": null}, {"id": "09119a78-3c6c-450b-a80f-454ac5c4a9b1", "title": "Personal Finance Course", "url": "https://www.coursera.org/learn/personal-finance", "author": "University of Illinois", "category": "FINANCIAL_LITERACY", "status": 404, "redirectUrl": null}, {"id": "87b38ea9-be38-460f-aa77-a71b777986f2", "title": "Product Analytics Fundamentals", "url": "https://amplitude.com/academy", "author": "Amplitude", "category": "PRODUCT_MANAGEMENT", "status": 404, "redirectUrl": null}, {"id": "fe46bf0c-2dd2-45dc-a47a-ed6e3e9e06f6", "title": "Product School Free Course", "url": "https://productschool.com/free-product-management-course/", "author": "Product School", "category": "PRODUCT_MANAGEMENT", "status": 404, "redirectUrl": null}, {"id": "48f71315-09eb-4243-84d8-52799f744cb9", "title": "Retirement Planning During Career Changes", "url": "https://www.fidelity.com/learning-center/personal-finance/retirement/career-change-retirement", "author": "Fidelity", "category": "FINANCIAL_LITERACY", "status": 403, "redirectUrl": null}, {"id": "82454cf2-4cff-43d1-ad58-35927253eed4", "title": "Salary Negotiation Masterclass", "url": "https://www.linkedin.com/learning/salary-negotiation", "author": "LinkedIn Learning", "category": "FINANCIAL_LITERACY", "status": 404, "redirectUrl": null}, {"id": "c759a83a-b755-40dd-b534-34cf23c8e196", "title": "Startup Funding and Investment", "url": "https://www.coursera.org/learn/venture-capital", "author": "University of Pennsylvania", "category": "ENTREPRENEURSHIP", "status": 404, "redirectUrl": null}, {"id": "239fbd7a-d890-4c74-95ca-68cbd26b5411", "title": "Technical Communication Skills", "url": "https://www.coursera.org/learn/technical-writing", "author": "Moscow Institute of Physics and Technology", "category": "LANGUAGE_LEARNING", "status": 404, "redirectUrl": null}, {"id": "40112f28-7b85-4684-8215-4c808012ff99", "title": "The Odin Project", "url": "https://www.theodinproject.com/", "author": "The Odin Project", "category": "WEB_DEVELOPMENT", "status": 403, "redirectUrl": null}], "redirected": [{"id": "e37d657c-9e78-405b-b020-239560fe3f53", "title": "AWS Cloud Practitioner Essentials", "url": "https://aws.amazon.com/training/digital/aws-cloud-practitioner-essentials/", "author": "Amazon Web Services", "category": "DEVOPS", "status": 301, "redirectUrl": "/training/learn-about/cloud-practitioner/"}, {"id": "37e78370-f958-485a-bd0a-6a2ffb70fcfd", "title": "Advanced Investment Strategies", "url": "https://www.edx.org/course/introduction-to-investments", "author": "Indian Institute of Management", "category": "FINANCIAL_LITERACY", "status": 301, "redirectUrl": "https://www.edx.org/learn/investing/indian-institute-of-management-bangalore-introduction-to-investments"}, {"id": "e83bb12d-ea31-460d-a9fc-326974c8f725", "title": "Android Development Fundamentals", "url": "https://developer.android.com/courses/android-basics-kotlin/course", "author": "Google Android", "category": "MOBILE_DEVELOPMENT", "status": 301, "redirectUrl": "/courses/android-basics-compose/course"}, {"id": "f4f685a0-5c6c-4520-98b5-8566ae92dcbf", "title": "Blockchain Fundamentals", "url": "https://www.edx.org/professional-certificate/uc-berkeleyx-blockchain-fundamentals", "author": "UC Berkeley", "category": "BLOCKCHAIN", "status": 302, "redirectUrl": "/certificates/professional-certificate/uc-berkeleyx-blockchain-fundamentals"}, {"id": "8a701bb2-42c0-472a-b174-b1903c89679b", "title": "Business Model Canvas", "url": "https://www.strategyzer.com/canvas/business-model-canvas", "author": "Strategyzer", "category": "ENTREPRENEURSHIP", "status": 301, "redirectUrl": "/library/business-model-canvas"}, {"id": "558e0f5a-58f9-47e2-86e0-0181b3812baf", "title": "Design Systems with Figma", "url": "https://www.figma.com/resources/learn-design-systems/", "author": "Figma", "category": "UX_UI_DESIGN", "status": 301, "redirectUrl": "https://www.figma.com/resource-library/design-basics/"}, {"id": "7c42f6ac-d3c8-4df8-a3ee-8e69c1c4ed6e", "title": "Financial Planning for Career Changes", "url": "https://bettermoneyhabits.bankofamerica.com/en/saving-budgeting/creating-a-budget", "author": "Bank of America", "category": "FINANCIAL_LITERACY", "status": 302, "redirectUrl": "https://www.bankofamerica.com/banking-information/error-page-en.html"}, {"id": "acfa659e-415a-4f25-8dc7-741456616a09", "title": "Flutter Development Bootcamp", "url": "https://flutter.dev/docs/get-started/codelab", "author": "Google Flutter", "category": "MOBILE_DEVELOPMENT", "status": 301, "redirectUrl": "https://docs.flutter.dev/get-started/codelab"}, {"id": "382b42e5-79ce-4a81-ad13-a8f744d7b9a5", "title": "Fundamentals of Digital Marketing", "url": "https://learndigital.withgoogle.com/digitalgarage/course/digital-marketing", "author": "Google Digital Garage", "category": "DIGITAL_MARKETING", "status": 301, "redirectUrl": "https://grow.google"}, {"id": "d537457d-b2c5-4041-af83-317f5c9033cd", "title": "Google Product Management Certificate", "url": "https://grow.google/certificates/product-management/", "author": "Google", "category": "PRODUCT_MANAGEMENT", "status": 302, "redirectUrl": "/intl/nl/"}, {"id": "770b6ed0-4eb2-4fe5-9787-09232a84a28c", "title": "Google UX Design Certificate", "url": "https://grow.google/certificates/ux-design/", "author": "Google", "category": "UX_UI_DESIGN", "status": 302, "redirectUrl": "/intl/nl/"}, {"id": "e16cf00e-f631-4b90-89cc-148922539b69", "title": "Investment Basics for Beginners", "url": "https://www.investopedia.com/university/beginner/", "author": "Investopedia", "category": "FINANCIAL_LITERACY", "status": 301, "redirectUrl": "https://www.investopedia.com/university/beginner"}, {"id": "4c091ece-e0f7-4e71-b8eb-4dbfc1445c45", "title": "MDN Web Docs", "url": "https://developer.mozilla.org/en-US/docs/Learn", "author": "Mozilla", "category": "WEB_DEVELOPMENT", "status": 301, "redirectUrl": "/en-US/docs/Learn_web_development"}, {"id": "624981d6-c7ef-450f-b402-12c6b1d575f0", "title": "MLOps Specialization", "url": "https://www.coursera.org/specializations/machine-learning-engineering-for-production-mlops", "author": "DeepLearning.AI", "category": "ARTIFICIAL_INTELLIGENCE", "status": 301, "redirectUrl": "https://www.coursera.org/learn/introduction-to-machine-learning-in-production"}, {"id": "f99bd221-a7d1-4f44-a262-fba66b5be876", "title": "Microsoft Azure Fundamentals", "url": "https://docs.microsoft.com/en-us/learn/paths/azure-fundamentals/", "author": "Microsoft", "category": "DEVOPS", "status": 301, "redirectUrl": "https://learn.microsoft.com/en-us/learn/paths/azure-fundamentals/"}, {"id": "1b90c410-29e9-4e9d-b9d0-f01b36cdfaff", "title": "Mobile UI/UX Design Principles", "url": "https://material.io/design/introduction", "author": "Google Material Design", "category": "MOBILE_DEVELOPMENT", "status": 301, "redirectUrl": "https://m2.material.io/design/introduction/"}, {"id": "ca24b7b2-33d1-44cf-992a-49e37ed9c6fd", "title": "Node.js Developer Roadmap", "url": "https://nodejs.org/en/learn", "author": "Node.js Foundation", "category": "WEB_DEVELOPMENT", "status": 307, "redirectUrl": "/en/learn/getting-started/introduction-to-nodejs"}, {"id": "1906a5bb-a5d4-4d55-adb5-9d6ddfb47743", "title": "Product Management Fundamentals", "url": "https://www.coursera.org/learn/uva-darden-product-management", "author": "University of Virginia", "category": "PRODUCT_MANAGEMENT", "status": 302, "redirectUrl": "/learn/uva-darden-digital-product-management"}, {"id": "6dc172a4-c3c4-4269-888a-4e6edfde4433", "title": "Professional Presentation Skills", "url": "https://www.toastmasters.org/education/pathways-learning-experience", "author": "Toastmasters International", "category": "LANGUAGE_LEARNING", "status": 302, "redirectUrl": "/sitecore/service/nolayout.aspx?item=%2feducation%2fpathways-learning-experience&layout=%7b00000000-0000-0000-0000-000000000000%7d&device=Default"}, {"id": "d9be23f8-b04a-460b-9fe6-a145ea94d67f", "title": "Project Management Foundations", "url": "https://www.linkedin.com/learning/project-management-foundations-2019", "author": "LinkedIn Learning", "category": "PROJECT_MANAGEMENT", "status": 301, "redirectUrl": "https://www.linkedin.com/learning/project-management-foundations-15528659"}, {"id": "f3b7b699-29a9-406a-bd6c-b6e3bfa9b743", "title": "Sales and Customer Acquisition", "url": "https://blog.hubspot.com/sales/sales-training", "author": "HubSpot", "category": "ENTREPRENEURSHIP", "status": 301, "redirectUrl": "https://www.hubspot.com/sales/sales-training"}, {"id": "************************************", "title": "Terraform Getting Started", "url": "https://learn.hashicorp.com/terraform", "author": "HashiCorp", "category": "DEVOPS", "status": 308, "redirectUrl": "https://developer.hashicorp.com/terraform/tutorials"}], "timeout": [{"id": "600486df-82b8-439b-b0e0-8952faf5fc9e", "title": "Adobe XD Tutorials", "url": "https://helpx.adobe.com/xd/tutorials.html", "author": "Adobe", "category": "UX_UI_DESIGN", "status": "TIMEOUT", "redirectUrl": null}], "errors": []}}